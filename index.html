<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grow Your Career - Animation & Digital Design Courses</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .navbar {
            padding: 1rem 0;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo h2 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            font-size: 1.8rem;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-menu a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s ease;
            position: relative;
            cursor: pointer;
        }

        .nav-menu a:hover {
            color: #667eea;
        }

        .nav-menu a::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -5px;
            left: 0;
            background: #667eea;
            transition: width 0.3s ease;
        }

        .nav-menu a:hover::after {
            width: 100%;
        }

        .hamburger {
            display: none;
            flex-direction: column;
            cursor: pointer;
        }

        .hamburger span {
            width: 25px;
            height: 3px;
            background: #333;
            margin: 3px 0;
            transition: 0.3s;
            border-radius: 2px;
        }

        .hamburger.active span:nth-child(1) {
            transform: rotate(-45deg) translate(-5px, 6px);
        }

        .hamburger.active span:nth-child(2) {
            opacity: 0;
        }

        .hamburger.active span:nth-child(3) {
            transform: rotate(45deg) translate(-5px, -6px);
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            opacity: 0.5;
        }

        .hero-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .hero-content {
            color: white;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            line-height: 1.2;
            margin-bottom: 1.5rem;
            animation: fadeInUp 1s ease;
        }

        .highlight {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            animation: fadeInUp 1s ease 0.2s both;
        }

        .quote-container {
            margin: 2rem 0;
            animation: fadeInUp 1s ease 0.4s both;
        }

        .hero-quote {
            font-style: italic;
            font-size: 1.1rem;
            border-left: 4px solid #ffd700;
            padding-left: 1rem;
            opacity: 0.9;
            margin: 0;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            margin-bottom: 3rem;
            animation: fadeInUp 1s ease 0.6s both;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 50px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            font-size: 1rem;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: #ffd700;
            color: #333;
        }

        .btn-primary:hover {
            background: #ffed4e;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }

        .btn-secondary {
            background: transparent;
            color: white;
            border: 2px solid white;
        }

        .btn-secondary:hover {
            background: white;
            color: #667eea;
            transform: translateY(-2px);
        }

        /* Lead Form Styles */
        .lead-form {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin: 2rem 0;
            animation: fadeInUp 1s ease 0.8s both;
        }

        .lead-form h3 {
            text-align: center;
            font-size: 1.3rem;
            margin-bottom: 1.5rem;
            color: white;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-input {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .form-input:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.3);
            border-color: #ffd700;
            box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.3);
        }

        .form-select {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 1rem;
            cursor: pointer;
        }

        .form-select option {
            background: #333;
            color: white;
        }

        .form-submit {
            width: 100%;
            padding: 15px;
            background: #ffd700;
            color: #333;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .form-submit:hover {
            background: #ffed4e;
            transform: translateY(-2px);
        }

        .form-submit:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }

        .form-message {
            margin-top: 1rem;
            padding: 12px;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
        }

        .form-message.success {
            background: rgba(34, 197, 94, 0.2);
            color: #dcfce7;
            border: 1px solid rgba(34, 197, 94, 0.3);
        }

        .form-message.error {
            background: rgba(239, 68, 68, 0.2);
            color: #fecaca;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .stats {
            display: flex;
            gap: 2rem;
            animation: fadeInUp 1s ease 1s both;
        }

        .stat {
            text-align: center;
        }

        .stat h3 {
            font-size: 2rem;
            font-weight: 700;
            color: #ffd700;
        }

        .stat p {
            opacity: 0.9;
            font-size: 0.9rem;
        }

        .hero-visual {
            position: relative;
            height: 400px;
        }

        .floating-elements {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .element {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 20px;
            font-weight: 700;
            font-size: 1.2rem;
            color: white;
            animation: float 6s ease-in-out infinite;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .element-1 {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .element-2 {
            top: 40%;
            right: 20%;
            animation-delay: 1.5s;
        }

        .element-3 {
            bottom: 30%;
            left: 20%;
            animation-delay: 3s;
        }

        .element-4 {
            top: 60%;
            right: 10%;
            animation-delay: 4.5s;
        }

        /* Courses Section */
        .courses {
            padding: 100px 0;
            background: #f8f9fa;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: #333;
        }

        .section-subtitle {
            text-align: center;
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 4rem;
        }

        .courses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .course-card {
            background: white;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .course-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .course-card:hover::before {
            left: 100%;
        }

        .course-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .course-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            transition: transform 0.3s ease;
        }

        .course-card:hover .course-icon {
            transform: scale(1.1);
        }

        .course-icon i {
            font-size: 2rem;
            color: white;
        }

        .course-card h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
        }

        .course-card p {
            color: #666;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .course-features {
            list-style: none;
            margin-bottom: 2rem;
            text-align: left;
        }

        .course-features li {
            padding: 0.5rem 0;
            color: #555;
            position: relative;
            padding-left: 1.5rem;
        }

        .course-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #667eea;
            font-weight: bold;
        }

        .btn-outline {
            background: transparent;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn-outline:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        /* About Section */
        .about {
            padding: 100px 0;
            background: white;
        }

        .about-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .about-text h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #333;
        }

        .about-text p {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.8;
        }

        .features {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .feature {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 1rem;
            border-radius: 10px;
            transition: background 0.3s ease;
        }

        .feature:hover {
            background: #f8f9fa;
        }

        .feature i {
            font-size: 1.5rem;
            color: #667eea;
            margin-top: 0.25rem;
            min-width: 24px;
        }

        .feature h4 {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .feature p {
            color: #666;
            margin: 0;
        }

        .about-quote {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 2rem;
            border-radius: 20px;
            color: white;
            text-align: center;
            position: relative;
        }

        .about-quote::before {
            content: '"';
            font-size: 4rem;
            position: absolute;
            top: -10px;
            left: 20px;
            opacity: 0.3;
        }

        .about-quote blockquote {
            font-size: 1.2rem;
            font-style: italic;
            margin-bottom: 1rem;
            line-height: 1.6;
            margin: 0 0 1rem 0;
        }

        .about-quote cite {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* Contact Section */
        .contact {
            padding: 100px 0;
            background: #f8f9fa;
        }

        .contact-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            margin-top: 3rem;
        }

        .contact-info {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .contact-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 1.5rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .contact-item:hover {
            transform: translateY(-5px);
        }

        .contact-item i {
            font-size: 1.5rem;
            color: #667eea;
            margin-top: 0.25rem;
            min-width: 24px;
        }

        .contact-item h4 {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .contact-item p {
            color: #666;
            margin: 0;
            line-height: 1.6;
        }

        .contact-form {
            background: white;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .contact-form .form-group {
            margin-bottom: 1.5rem;
        }

        .contact-form .form-input,
        .contact-form .form-select,
        .contact-form .form-textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            font-family: 'Poppins', sans-serif;
            background: white;
            color: #333;
        }

        .contact-form .form-input:focus,
        .contact-form .form-select:focus,
        .contact-form .form-textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .contact-form .form-textarea {
            resize: vertical;
            min-height: 120px;
        }

        .contact-form .form-submit {
            background: #667eea;
            color: white;
        }

        .contact-form .form-submit:hover {
            background: #5a67d8;
        }

        /* Footer */
        .footer {
            background: #1a1a1a;
            color: white;
            padding: 60px 0 20px;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3,
        .footer-section h4 {
            margin-bottom: 1rem;
            color: #ffd700;
        }

        .footer-section p {
            color: #ccc;
            line-height: 1.6;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 0.5rem;
        }

        .footer-section ul li a {
            color: #ccc;
            text-decoration: none;
            transition: color 0.3s ease;
            cursor: pointer;
        }

        .footer-section ul li a:hover {
            color: #ffd700;
        }

        .social-links {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }

        .social-links a {
            width: 40px;
            height: 40px;
            background: #333;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .social-links a:hover {
            background: #667eea;
            transform: translateY(-2px);
        }

        .footer-contact p {
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .footer-contact i {
            color: #ffd700;
            width: 20px;
        }

        .map-container {
            margin-top: 1rem;
            border-radius: 10px;
            overflow: hidden;
        }

        .footer-bottom {
            border-top: 1px solid #333;
            padding-top: 1rem;
            text-align: center;
            color: #999;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            animation: modalSlideIn 0.3s ease;
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            margin: 0;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 1.5rem;
        }

        .modal-body {
            padding: 2rem;
        }

        .modal-body h3 {
            color: #333;
            margin: 1.5rem 0 0.5rem;
            font-size: 1.2rem;
        }

        .modal-body p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .close {
            color: white;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s ease;
            background: none;
            border: none;
        }

        .close:hover {
            opacity: 0.7;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hamburger {
                display: flex;
            }
            
            .nav-menu {
                position: fixed;
                left: -100%;
                top: 70px;
                flex-direction: column;
                background-color: white;
                width: 100%;
                text-align: center;
                transition: 0.3s;
                box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
                padding: 2rem 0;
            }
            
            .nav-menu.active {
                left: 0;
            }
            
            .hero-container {
                grid-template-columns: 1fr;
                text-align: center;
                gap: 2rem;
            }
            
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .form-row {
                grid-template-columns: 1fr;
            }
            
            .stats {
                justify-content: center;
                flex-wrap: wrap;
            }
            
            .about-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .courses-grid {
                grid-template-columns: 1fr;
            }
            
            .contact-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .footer-content {
                grid-template-columns: 1fr;
                text-align: center;
            }
            
            .modal-content {
                width: 95%;
                margin: 10% auto;
            }
            
            .section-title {
                font-size: 2rem;
            }
        }

        @media (max-width: 480px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .section-title {
                font-size: 1.8rem;
            }
            
            .stats {
                flex-direction: column;
                gap: 1rem;
            }
            
            .hero-buttons {
                gap: 0.5rem;
            }
            
            .btn {
                padding: 10px 20px;
                font-size: 0.9rem;
            }

            .lead-form {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="logo">
                    <h2>Grow Your Career</h2>
                </div>
                <ul class="nav-menu">
                    <li><a onclick="scrollToSection('home')">Home</a></li>
                    <li><a onclick="scrollToSection('courses')">Courses</a></li>
                    <li><a onclick="scrollToSection('about')">About</a></li>
                    <li><a onclick="scrollToSection('contact')">Contact</a></li>
                </ul>
                <div class="hamburger" onclick="toggleMobileMenu()">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1 class="hero-title">
                    Transform Your Passion Into a 
                    <span class="highlight">Creative Career</span>
                </h1>

                <p class="hero-subtitle">
                    Master the art of animation, VFX, and digital design with industry-leading courses
                </p>

                <div class="quote-container">
                    <blockquote class="hero-quote">
                        "Every great animation starts with a single frame of imagination"
                    </blockquote>
                </div>

                <div class="hero-buttons">
                    <button class="btn btn-primary" onclick="scrollToSection('courses')">Start Your Journey</button>
                    <button class="btn btn-secondary" onclick="scrollToSection('courses')">View Courses</button>
                </div>

                <!-- Lead Form -->
                <div class="lead-form">
                    <h3>Get Free Course Information</h3>
                    <form id="leadForm" onsubmit="handleLeadFormSubmit(event)">
                        <div class="form-row">
                            <div class="form-group">
                                <input type="text" class="form-input" name="leadName" placeholder="Your Name" required>
                            </div>
                            <div class="form-group">
                                <input type="email" class="form-input" name="leadEmail" placeholder="Your Email" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <input type="tel" class="form-input" name="leadPhone" placeholder="Your Phone" required>
                            </div>
                            <div class="form-group">
                                <select class="form-select" name="leadCourse" required>
                                    <option value="">Select Course</option>
                                    <option value="3d-animation">3D Animation</option>
                                    <option value="vfx">VFX (Visual Effects)</option>
                                    <option value="digital-design">Digital Design</option>
                                    <option value="game-art">Game Art</option>
                                    <option value="ar-vr">AR/VR Development</option>
                                </select>
                            </div>
                        </div>
                        <button type="submit" class="form-submit" id="leadSubmitBtn">Get Free Course Info</button>
                    </form>
                    <div id="leadFormMessage" class="form-message" style="display: none;"></div>
                </div>

                <div class="stats">
                    <div class="stat">
                        <h3 data-target="500">0</h3>
                        <p>Students Trained</p>
                    </div>
                    <div class="stat">
                        <h3 data-target="95">0</h3>
                        <p>Job Placement %</p>
                    </div>
                    <div class="stat">
                        <h3 data-target="50">0</h3>
                        <p>Industry Partners</p>
                    </div>
                </div>
            </div>

            <div class="hero-visual">
                <div class="floating-elements">
                    <div class="element element-1">3D</div>
                    <div class="element element-2">VFX</div>
                    <div class="element element-3">AR/VR</div>
                    <div class="element element-4">Design</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Courses Section -->
    <section id="courses" class="courses">
        <div class="container">
            <h2 class="section-title">Our Premium Courses</h2>
            <p class="section-subtitle">Choose your path to creative excellence</p>
            
            <div class="courses-grid">
                <div class="course-card">
                    <div class="course-icon">
                        <i class="fas fa-cube"></i>
                    </div>
                    <h3>3D Animation</h3>
                    <p>Master the fundamentals of 3D modeling, rigging, and animation using industry-standard software like Maya and Blender.</p>
                    <ul class="course-features">
                        <li>Character Animation</li>
                        <li>3D Modeling</li>
                        <li>Rigging & Skinning</li>
                        <li>Lighting & Rendering</li>
                    </ul>
                    <button class="btn btn-outline">Learn More</button>
                </div>
                
                <div class="course-card">
                    <div class="course-icon">
                        <i class="fas fa-magic"></i>
                    </div>
                    <h3>VFX (Visual Effects)</h3>
                    <p>Create stunning visual effects for films, commercials, and digital media using cutting-edge VFX techniques.</p>
                    <ul class="course-features">
                        <li>Compositing</li>
                        <li>Motion Graphics</li>
                        <li>Particle Systems</li>
                        <li>Green Screen</li>
                    </ul>
                    <button class="btn btn-outline">Learn More</button>
                </div>
                
                <div class="course-card">
                    <div class="course-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3>Digital Design</h3>
                    <p>Develop your skills in graphic design, UI/UX, and digital illustration for modern creative industries.</p>
                    <ul class="course-features">
                        <li>UI/UX Design</li>
                        <li>Brand Identity</li>
                        <li>Digital Illustration</li>
                        <li>Web Design</li>
                    </ul>
                    <button class="btn btn-outline">Learn More</button>
                </div>
                
                <div class="course-card">
                    <div class="course-icon">
                        <i class="fas fa-gamepad"></i>
                    </div>
                    <h3>Game Art</h3>
                    <p>Create immersive game environments, characters, and assets for the booming gaming industry.</p>
                    <ul class="course-features">
                        <li>Environment Art</li>
                        <li>Character Design</li>
                        <li>Texture Painting</li>
                        <li>Game Engines</li>
                    </ul>
                    <button class="btn btn-outline">Learn More</button>
                </div>
                
                <div class="course-card">
                    <div class="course-icon">
                        <i class="fas fa-vr-cardboard"></i>
                    </div>
                    <h3>AR/VR Development</h3>
                    <p>Step into the future with augmented and virtual reality development for next-generation experiences.</p>
                    <ul class="course-features">
                        <li>Unity Development</li>
                        <li>AR Applications</li>
                        <li>VR Experiences</li>
                        <li>Interactive Design</li>
                    </ul>
                    <button class="btn btn-outline">Learn More</button>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
        <div class="container">
            <div class="about-content">
                <div class="about-text">
                    <h2>Why Choose Grow Your Career?</h2>
                    <p>We're not just another training institute. We're your partners in creative transformation, dedicated to nurturing the next generation of digital artists and animators.</p>
                    
                    <div class="features">
                        <div class="feature">
                            <i class="fas fa-award"></i>
                            <div>
                                <h4>Industry-Certified Instructors</h4>
                                <p>Learn from professionals working in top animation studios</p>
                            </div>
                        </div>
                        <div class="feature">
                            <i class="fas fa-laptop-code"></i>
                            <div>
                                <h4>Hands-on Projects</h4>
                                <p>Build a portfolio with real-world projects and assignments</p>
                            </div>
                        </div>
                        <div class="feature">
                            <i class="fas fa-users"></i>
                            <div>
                                <h4>Career Support</h4>
                                <p>Job placement assistance and industry networking opportunities</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="about-quote">
                    <blockquote>
                        "Creativity is intelligence having fun. At Grow Your Career, we believe every student has the potential to create magic."
                    </blockquote>
                    <cite>- Our Philosophy</cite>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <h2 class="section-title">Get In Touch</h2>
            <p class="section-subtitle">Ready to start your creative journey? Contact us today!</p>
            
            <div class="contact-content">
                <div class="contact-info">
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <div>
                            <h4>Visit Us</h4>
                            <p>123 Creative Street, Animation District<br>Mumbai, Maharashtra 400001, India</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <div>
                            <h4>Call Us</h4>
                            <p>+91 98765 43210<br>+91 87654 32109</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <div>
                            <h4>Email Us</h4>
                            <p><EMAIL><br><EMAIL></p>
                        </div>
                    </div>
                </div>
                
                <div class="contact-form">
                    <form id="contactForm" onsubmit="handleContactFormSubmit(event)">
                        <div class="form-group">
                            <input type="text" class="form-input" name="name" placeholder="Your Name" required>
                        </div>
                        <div class="form-group">
                            <input type="email" class="form-input" name="email" placeholder="Your Email" required>
                        </div>
                        <div class="form-group">
                            <input type="tel" class="form-input" name="phone" placeholder="Your Phone" required>
                        </div>
                        <div class="form-group">
                            <select class="form-select" name="course" required>
                                <option value="">Select Course Interest</option>
                                <option value="3d-animation">3D Animation</option>
                                <option value="vfx">VFX (Visual Effects)</option>
                                <option value="digital-design">Digital Design</option>
                                <option value="game-art">Game Art</option>
                                <option value="ar-vr">AR/VR Development</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <textarea class="form-textarea" name="message" placeholder="Your Message" rows="5"></textarea>
                        </div>
                        <button type="submit" class="form-submit" id="contactSubmitBtn">Send Message</button>
                    </form>
                    <div id="contactFormMessage" class="form-message" style="display: none;"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Grow Your Career</h3>
                    <p>Empowering creative minds with cutting-edge animation and design education.</p>
                    <div class="social-links">
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
                        <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a onclick="scrollToSection('courses')">Courses</a></li>
                        <li><a onclick="scrollToSection('about')">About Us</a></li>
                        <li><a onclick="scrollToSection('contact')">Contact</a></li>
                        <li><a href="#">Admissions</a></li>
                        <li><a href="#">Gallery</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Legal</h4>
                    <ul>
                        <li><a onclick="openModal('terms')">Terms & Conditions</a></li>
                        <li><a onclick="openModal('privacy')">Privacy Policy</a></li>
                        <li><a href="#">Refund Policy</a></li>
                        <li><a href="#">Student Guidelines</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Our Location</h4>
                    <div class="footer-contact">
                        <p><i class="fas fa-map-marker-alt"></i> 123 Creative Street, Animation District, Mumbai, India</p>
                        <p><i class="fas fa-phone"></i> +91 98765 43210</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    </div>
                    <div class="map-container">
                        <iframe 
                            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3771.2!2d72.8777!3d19.0760!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3be7c9e20c7d1c63%3A0x2c1b9b2c1c1c1c1c!2sMumbai%2C%20Maharashtra%2C%20India!5e0!3m2!1sen!2sus!4v1234567890123"
                            width="100%" 
                            height="150" 
                            style="border:0;" 
                            allowfullscreen="" 
                            loading="lazy"
                            referrerpolicy="no-referrer-when-downgrade">
                        </iframe>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Grow Your Career. All rights reserved. | Designed with ❤️ for aspiring creators</p>
            </div>
        </div>
    </footer>

    <!-- Modals -->
    <div id="termsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Terms & Conditions</h2>
                <button class="close" onclick="closeModal('terms')">&times;</button>
            </div>
            <div class="modal-body">
                <h3>1. Course Enrollment</h3>
                <p>Students must complete the enrollment process and pay applicable fees before accessing course materials. All admissions are subject to availability and eligibility criteria.</p>
                
                <h3>2. Attendance Policy</h3>
                <p>Regular attendance is mandatory for successful completion. Students with less than 75% attendance may not be eligible for certification and may be required to repeat the course.</p>
                
                <h3>3. Payment Terms</h3>
                <p>Course fees must be paid as per the agreed schedule. Late payments may result in suspension of access to classes and materials. No refunds will be provided after 30 days of course commencement.</p>
                
                <h3>4. Intellectual Property</h3>
                <p>All course materials, including videos, documents, and software, are proprietary to Grow Your Career and cannot be redistributed, copied, or shared without written permission.</p>
                
                <h3>5. Code of Conduct</h3>
                <p>Students are expected to maintain professional behavior and respect towards instructors and fellow students. Any form of harassment or misconduct will result in immediate termination.</p>
                
                <h3>6. Certification</h3>
                <p>Certificates will be awarded only upon successful completion of all course requirements, including assignments, projects, and assessments.</p>
            </div>
        </div>
    </div>

    <div id="privacyModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Privacy Policy</h2>
                <button class="close" onclick="closeModal('privacy')">&times;</button>
            </div>
            <div class="modal-body">
                <h3>Information Collection</h3>
                <p>We collect personal information necessary for course enrollment, communication, and educational services. This includes name, contact details, educational background, and payment information.</p>
                
                <h3>Data Usage</h3>
                <p>Your information is used solely for educational services, communication regarding courses, and administrative purposes. We may also use aggregated data for improving our services.</p>
                
                <h3>Data Security</h3>
                <p>We implement appropriate technical and organizational security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>
                
                <h3>Third-Party Sharing</h3>
                <p>We do not sell, trade, or share your personal information with third parties except for essential service providers (payment processors, email services) who are bound by confidentiality agreements.</p>
                
                <h3>Data Retention</h3>
                <p>We retain your personal information for as long as necessary to provide services and comply with legal obligations. You may request deletion of your data at any time.</p>
                
                <h3>Contact Information</h3>
                <p>For privacy-related queries or to exercise your data rights, contact <NAME_EMAIL> or call +91 98765 43210.</p>
            </div>
        </div>
    </div>

    <script>
        // Mobile Navigation
        function toggleMobileMenu() {
            const hamburger = document.querySelector('.hamburger');
            const navMenu = document.querySelector('.nav-menu');
            
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        }

        // Smooth Scrolling
        function scrollToSection(sectionId) {
            const element = document.getElementById(sectionId);
            if (element) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = element.offsetTop - headerHeight;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
            
            // Close mobile menu if open
            const hamburger = document.querySelector('.hamburger');
            const navMenu = document.querySelector('.nav-menu');
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        }

        // Header Scroll Effect
        window.addEventListener('scroll', () => {
            const header = document.querySelector('.header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
                header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.15)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
            }
        });

        // Counter Animation
        function animateCounter(element, target, duration = 2000) {
            let start = 0;
            const increment = target / (duration / 16);
            
            function updateCounter() {
                start += increment;
                if (start < target) {
                    element.textContent = Math.floor(start);
                    requestAnimationFrame(updateCounter);
                } else {
                    element.textContent = target;
                }
            }
            
            updateCounter();
        }

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    if (entry.target.classList.contains('stats')) {
                        const statNumbers = entry.target.querySelectorAll('.stat h3');
                        statNumbers.forEach(stat => {
                            const target = parseInt(stat.getAttribute('data-target'));
                            animateCounter(stat, target);
                        });
                        observer.unobserve(entry.target);
                    } else {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.addEventListener('DOMContentLoaded', () => {
            const statsSection = document.querySelector('.stats');
            if (statsSection) {
                observer.observe(statsSection);
            }

            document.querySelectorAll('.course-card, .feature, .about-quote, .contact-item').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                el.style.transition = 'all 0.6s ease';
                observer.observe(el);
            });
        });

        // Lead Form Submission
        function handleLeadFormSubmit(event) {
            event.preventDefault();
            
            const form = event.target;
            const formData = new FormData(form);
            const submitBtn = document.getElementById('leadSubmitBtn');
            const messageDiv = document.getElementById('leadFormMessage');
            
            // Basic validation
            const name = formData.get('leadName');
            const email = formData.get('leadEmail');
            const phone = formData.get('leadPhone');
            const course = formData.get('leadCourse');
            
            if (!name || !email || !phone || !course) {
                showMessage(messageDiv, 'Please fill in all fields.', 'error');
                return;
            }
            
            // Show loading state
            submitBtn.textContent = 'Sending...';
            submitBtn.disabled = true;
            
            // Simulate form submission
            setTimeout(() => {
                showMessage(messageDiv, 'Thank you! We\'ll send you detailed course information within 24 hours.', 'success');
                form.reset();
                submitBtn.textContent = 'Get Free Course Info';
                submitBtn.disabled = false;
                
                // Clear message after 5 seconds
                setTimeout(() => {
                    hideMessage(messageDiv);
                }, 5000);
            }, 2000);
        }

        // Contact Form Submission
        function handleContactFormSubmit(event) {
            event.preventDefault();
            
            const form = event.target;
            const formData = new FormData(form);
            const submitBtn = document.getElementById('contactSubmitBtn');
            const messageDiv = document.getElementById('contactFormMessage');
            
            // Basic validation
            const name = formData.get('name');
            const email = formData.get('email');
            const phone = formData.get('phone');
            const course = formData.get('course');
            
            if (!name || !email || !phone || !course) {
                showMessage(messageDiv, 'Please fill in all required fields.', 'error');
                return;
            }
            
            // Show loading state
            submitBtn.textContent = 'Sending...';
            submitBtn.disabled = true;
            
            // Simulate form submission
            setTimeout(() => {
                showMessage(messageDiv, 'Thank you for your message! We will get back to you soon.', 'success');
                form.reset();
                submitBtn.textContent = 'Send Message';
                submitBtn.disabled = false;
                
                // Clear message after 5 seconds
                setTimeout(() => {
                    hideMessage(messageDiv);
                }, 5000);
            }, 2000);
        }

        // Message Display Functions
        function showMessage(element, message, type) {
            element.textContent = message;
            element.className = `form-message ${type}`;
            element.style.display = 'block';
        }

        function hideMessage(element) {
            element.style.display = 'none';
        }

        // Modal Functions
        function openModal(type) {
            const modal = document.getElementById(type + 'Modal');
            if (modal) {
                modal.style.display = 'block';
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(type) {
            const modal = document.getElementById(type + 'Modal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        }

        // Close modal when clicking outside
        window.addEventListener('click', (event) => {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape') {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    if (modal.style.display === 'block') {
                        modal.style.display = 'none';
                        document.body.style.overflow = 'auto';
                    }
                });
            }
        });

        // Console Welcome Message
        console.log(`
🎨 Welcome to Grow Your Career!
🚀 Ready to transform your creative passion into a career?
💡 Check out our amazing courses and start your journey today!

Built with ❤️ for aspiring animators and designers.
        `);
    </script>
</body>
</html>