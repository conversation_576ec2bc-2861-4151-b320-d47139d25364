* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h2 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
    cursor: pointer;
}

.nav-menu a:hover {
    color: #667eea;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 0;
    background: #667eea;
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

.hamburger.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    opacity: 0.5;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-content {
    color: white;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    animation: fadeInUp 1s ease;
}

.highlight {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    animation: fadeInUp 1s ease 0.2s both;
}

.quote-container {
    margin: 2rem 0;
    animation: fadeInUp 1s ease 0.4s both;
}

.hero-quote {
    font-style: italic;
    font-size: 1.1rem;
    border-left: 4px solid #ffd700;
    padding-left: 1rem;
    opacity: 0.9;
    margin: 0;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
    animation: fadeInUp 1s ease 0.6s both;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    font-size: 1rem;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: #ffd700;
    color: #333;
}

.btn-primary:hover {
    background: #ffed4e;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: white;
    color: #667eea;
    transform: translateY(-2px);
}

/* Lead Form Styles */
.lead-form {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin: 2rem 0;
    animation: fadeInUp 1s ease 0.8s both;
}

.lead-form h3 {
    text-align: center;
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    color: white;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.form-input:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.3);
    border-color: #ffd700;
    box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.3);
}

.form-select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 1rem;
    cursor: pointer;
}

.form-select option {
    background: #333;
    color: white;
}

.form-submit {
    width: 100%;
    padding: 15px;
    background: #ffd700;
    color: #333;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-submit:hover {
    background: #ffed4e;
    transform: translateY(-2px);
}

.form-submit:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.form-message {
    margin-top: 1rem;
    padding: 12px;
    border-radius: 8px;
    text-align: center;
    font-weight: 500;
}

.form-message.success {
    background: rgba(34, 197, 94, 0.2);
    color: #dcfce7;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.form-message.error {
    background: rgba(239, 68, 68, 0.2);
    color: #fecaca;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.stats {
    display: flex;
    gap: 2rem;
    animation: fadeInUp 1s ease 1s both;
}

.stat {
    text-align: center;
}

.stat h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #ffd700;
}

.stat p {
    opacity: 0.9;
    font-size: 0.9rem;
}

.hero-visual {
    position: relative;
    height: 400px;
}

.floating-elements {
    position: relative;
    width: 100%;
    height: 100%;
}

.element {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 20px;
    font-weight: 700;
    font-size: 1.2rem;
    color: white;
    animation: float 6s ease-in-out infinite;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.element-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.element-2 {
    top: 40%;
    right: 20%;
    animation-delay: 1.5s;
}

.element-3 {
    bottom: 30%;
    left: 20%;
    animation-delay: 3s;
}

.element-4 {
    top: 60%;
    right: 10%;
    animation-delay: 4.5s;
}

/* Courses Section */
.courses {
    padding: 100px 0;
    background: #f8f9fa;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #333;
}

.section-subtitle {
    text-align: center;
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 4rem;
}

.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.course-card {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.course-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.course-card:hover::before {
    left: 100%;
}

.course-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.course-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: transform 0.3s ease;
}

.course-card:hover .course-icon {
    transform: scale(1.1);
}

.course-icon i {
    font-size: 2rem;
    color: white;
}

.course-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
}

.course-card p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.course-features {
    list-style: none;
    margin-bottom: 2rem;
    text-align: left;
}

.course-features li {
    padding: 0.5rem 0;
    color: #555;
    position: relative;
    padding-left: 1.5rem;
}

.course-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #667eea;
    font-weight: bold;
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

/* About Section */
.about {
    padding: 100px 0;
    background: white;
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: #333;
}

.about-text p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.8;
}

.features {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.feature {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    border-radius: 10px;
    transition: background 0.3s ease;
}

.feature:hover {
    background: #f8f9fa;
}

.feature i {
    font-size: 1.5rem;
    color: #667eea;
    margin-top: 0.25rem;
    min-width: 24px;
}

.feature h4 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.feature p {
    color: #666;
    margin: 0;
}

.about-quote {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem;
    border-radius: 20px;
    color: white;
    text-align: center;
    position: relative;
}

.about-quote::before {
    content: '"';
    font-size: 4rem;
    position: absolute;
    top: -10px;
    left: 20px;
    opacity: 0.3;
}

.about-quote blockquote {
    font-size: 1.2rem;
    font-style: italic;
    margin-bottom: 1rem;
    line-height: 1.6;
    margin: 0 0 1rem 0;
}

.about-quote cite {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Contact Section */
.contact {
    padding: 100px 0;
    background: #f8f9fa;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-top: 3rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-5px);
}

.contact-item i {
    font-size: 1.5rem;
    color: #667eea;
    margin-top: 0.25rem;
    min-width: 24px;
}

.contact-item h4 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.contact-item p {
    color: #666;
    margin: 0;
    line-height: 1.6;
}

.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.contact-form .form-group {
    margin-bottom: 1.5rem;
}

.contact-form .form-input,
.contact-form .form-select,
.contact-form .form-textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    font-family: 'Poppins', sans-serif;
    background: white;
    color: #333;
}

.contact-form .form-input:focus,
.contact-form .form-select:focus,
.contact-form .form-textarea:focus {
    outline: none;
    border-color: #667eea;
}

.contact-form .form-textarea {
    resize: vertical;
    min-height: 120px;
}

.contact-form .form-submit {
    background: #667eea;
    color: white;
}

.contact-form .form-submit:hover {
    background: #5a67d8;
}

/* Footer */
.footer {
    background: #1a1a1a;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #ffd700;
}

.footer-section p {
    color: #ccc;
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
    cursor: pointer;
}

.footer-section ul li a:hover {
    color: #ffd700;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: #333;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #667eea;
    transform: translateY(-2px);
}

.footer-contact p {
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-contact i {
    color: #ffd700;
    width: 20px;
}

.map-container {
    margin-top: 1rem;
    border-radius: 10px;
    overflow: hidden;
}

.footer-bottom {
    border-top: 1px solid #333;
    padding-top: 1rem;
    text-align: center;
    color: #999;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 80%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    margin: 0;
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.modal-body {
    padding: 2rem;
}

.modal-body h3 {
    color: #333;
    margin: 1.5rem 0 0.5rem;
    font-size: 1.2rem;
}

.modal-body p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.close {
    color: white;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: opacity 0.3s ease;
    background: none;
    border: none;
}

.close:hover {
    opacity: 0.7;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .stats {
        justify-content: center;
        flex-wrap: wrap;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .courses-grid {
        grid-template-columns: 1fr;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .section-title {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .stats {
        flex-direction: column;
        gap: 1rem;
    }

    .hero-buttons {
        gap: 0.5rem;
    }

    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .lead-form {
        padding: 1.5rem;
    }
}
